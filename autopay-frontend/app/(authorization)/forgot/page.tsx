'use client'

import <PERSON><PERSON> from '@/components/custom-ui/lottie'
import { Card, CardContent } from '@/components/ui/card'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

import { Icons } from '@/components/icons'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useDomainType } from '@/lib/hooks/useDomainType'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'

const formSchema = z.object({
  email: z.string().email('Vui lòng nhập địa chỉ email hợp lệ'),
})

type FormValues = z.infer<typeof formSchema>

export default function Component() {
  const [successMessage, setSuccessMessage] = useState<string>('')
  const { isMappedDomain } = useDomainType()
  const router = useRouter()

  // Redirect mapped domains to member forgot password
  useEffect(() => {
    if (isMappedDomain) {
      router.replace('/member/forgot')
      return
    }
  }, [isMappedDomain, router])

  // System users only use /forgot endpoint
  const endpoint = '/forgot'

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  })

  // Forgot password mutation
  const { isPending, mutate: forgotPasswordMutation } = useMutation<ApiResponse, Error, FormValues>({
    mutationFn: async (data: FormValues) => {
      return queryFetchHelper(endpoint, {
        method: 'POST',
        body: JSON.stringify(data),
      })
    },
    onMutate: () => {
      setSuccessMessage('')
    },
    onSuccess: (data) => {
      const message = data.message || 'Vui lòng kiểm tra email để hoàn tất quá trình đặt lại mật khẩu.'
      setSuccessMessage(message)
      toast.success(message)
      form.reset()
    },
    onError: (error) => {
      toast.error(error.message || 'Gửi email đặt lại mật khẩu thất bại')

      form.setError('email', {
        type: 'manual',
        message: error.message,
      })
    },
  })

  const onSubmit = (data: FormValues) => {
    forgotPasswordMutation(data)
  }

  return (
    <div className="max-w-lg space-y-4">
      <Card className="mx-4 md:mx-0 md:border-0 md:shadow-none">
        <CardContent>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="w-full space-y-4 rounded-lg"
              autoComplete="off"
              noValidate>
              <div>
                <h3 className="text-2xl font-bold">Quên mật khẩu?</h3>
                <p className="text-muted-foreground">
                  {isMappedDomain
                    ? 'Không sao, chỉ cần nhập địa chỉ email khách hàng của bạn.'
                    : 'Không sao, chỉ cần nhập địa chỉ email của bạn.'}
                  <br />
                  Liên kết đặt lại mật khẩu sẽ được gửi đến email của bạn ngay lập tức.
                </p>
              </div>

              {successMessage && (
                <Alert className="dark:bg-accent border-green-500 bg-green-50">
                  <AlertDescription dangerouslySetInnerHTML={{ __html: successMessage }} />
                </Alert>
              )}

              <div className="mt-5 flex flex-col gap-2">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-semibold">Email</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="Nhập email của bạn"
                          autoComplete="off"
                          tabIndex={1}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="mt-5 flex items-center gap-4">
                <Button
                  type="submit"
                  disabled={isPending}
                  className="flex flex-1 items-center gap-2"
                  tabIndex={3}>
                  {isPending && <Icons.spinner className="h-4 w-4 animate-spin" />}
                  Xác nhận
                </Button>
                <Link
                  href="/login"
                  className="text-muted-foreground hover:text-foreground transition-colors">
                  Quay lại Đăng nhập
                </Link>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
