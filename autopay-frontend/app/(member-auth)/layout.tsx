import { generateDynamicMetadata } from '@/lib/utils/metadata'
import type { Metadata } from 'next'
import { ReactNode } from 'react'

export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata('Member Authentication')
}

interface LayoutProps {
  children: ReactNode
}

export default function MemberAuthLayout({ children }: LayoutProps) {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md space-y-8">{children}</div>
    </div>
  )
}
