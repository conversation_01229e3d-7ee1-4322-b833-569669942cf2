'use client'

import { Icons } from '@/components/icons'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import Link from 'next/link'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { IoIosSend } from 'react-icons/io'
import { toast } from 'sonner'
import { z } from 'zod'

export default function MemberForgotPasswordPage() {
  const [successMessage, setSuccessMessage] = useState<string>('')

  const formSchema = z.object({
    email: z.string().email({
      message: 'Email không hợp lệ',
    }),
  })

  type FormValues = z.infer<typeof formSchema>

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  })

  const { isPending, mutate } = useMutation({
    mutationFn: (data: FormValues) => {
      return queryFetchHelper('/member/forgot', {
        method: 'POST',
        body: JSON.stringify(data),
      })
    },
    onSuccess: (data) => {
      if (data.success) {
        setSuccessMessage(data.message)
        toast.success(data.message)
        form.reset()
      } else {
        toast.error(data.message)
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.message || 'Đã xảy ra lỗi'
      toast.error(errorMessage)
    },
  })

  const onSubmit = (data: FormValues) => {
    mutate(data)
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl text-center">Quên mật khẩu</CardTitle>
        <CardDescription className="text-center">
          Nhập email để nhận liên kết đặt lại mật khẩu
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {successMessage && (
          <Alert>
            <AlertDescription>{successMessage}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      disabled={isPending}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isPending}>
              {isPending ? (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <IoIosSend className="mr-2 h-4 w-4" />
              )}
              Gửi liên kết đặt lại
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <div className="text-sm text-center space-y-2">
          <Link href="/member/login" className="text-primary hover:underline">
            Quay lại đăng nhập
          </Link>
          <div>
            Chưa có tài khoản?{' '}
            <Link href="/member/register" className="text-primary hover:underline">
              Đăng ký ngay
            </Link>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
