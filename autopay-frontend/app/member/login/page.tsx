'use client'

import FormItem<PERSON>asswordGenerator from '@/components/custom-ui/form-item-password-generator'
import { Icons } from '@/components/icons'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { cleanAuthorizationData } from '@/lib/utils'
import emitter from '@/lib/utils/eventBus'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import Cookies from 'js-cookie'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { IoIosSend } from 'react-icons/io'
import { toast } from 'sonner'
import { z } from 'zod'

export default function MemberLoginPage() {
  const router = useRouter()
  const [message, setMessage] = useState<string>('')
  const [verificationNeeded, setVerificationNeeded] = useState<string | null>(null)

  const FormSchema = z.object({
    email: z.string().email({
      message: 'Email không hợp lệ',
    }),
    password: z.string().min(8, {
      message: 'Mật khẩu phải có ít nhất 8 ký tự',
    }),
    remember: z.boolean(),
  })

  type FormValues = z.infer<typeof FormSchema>

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: '',
      password: '',
      remember: false,
    },
  })

  const { isPending, mutate: loginMutation } = useMutation({
    mutationFn: (data: FormValues) => {
      return queryFetchHelper('/member/login', {
        method: 'POST',
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          remember: data.remember,
        }),
      })
    },
    onMutate: () => {
      setMessage('')
    },
    onSuccess: (data) => {
      if (data.success) {
        // Store authorization token
        Cookies.set(process.env.NEXT_PUBLIC_APP_NAME + '.authorization', data.data.token, {
          expires: form.getValues('remember') ? 365 : 1,
        })

        // Store user data
        Cookies.set(process.env.NEXT_PUBLIC_APP_NAME + '.user', JSON.stringify(data.data.member))

        // Emit login event
        emitter.emit('userLoggedIn', data.data.member)

        toast.success(data.message)
        router.push('/dashboard')
      } else {
        if (data.data?.verification_needed) {
          setVerificationNeeded(data.data.verification_needed)
        }
        setMessage(data.message)
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.message || 'Đã xảy ra lỗi'
      setMessage(errorMessage)
      toast.error(errorMessage)
    },
  })

  const onSubmit = (data: FormValues) => {
    loginMutation(data)
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl text-center">Đăng nhập thành viên</CardTitle>
        <CardDescription className="text-center">
          Nhập email và mật khẩu để truy cập tài khoản thành viên
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {message && (
          <Alert variant="destructive">
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        {verificationNeeded && (
          <Alert>
            <AlertDescription>
              Tài khoản cần xác thực email. Vui lòng kiểm tra email của bạn.
            </AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      disabled={isPending}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mật khẩu</FormLabel>
                  <FormControl>
                    <FormItemPasswordGenerator
                      placeholder="Nhập mật khẩu"
                      disabled={isPending}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="remember"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isPending}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Ghi nhớ đăng nhập</FormLabel>
                  </div>
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isPending}>
              {isPending ? (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <IoIosSend className="mr-2 h-4 w-4" />
              )}
              Đăng nhập
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <div className="text-sm text-center space-y-2">
          <Link href="/member/forgot" className="text-primary hover:underline">
            Quên mật khẩu?
          </Link>
          <div>
            Chưa có tài khoản?{' '}
            <Link href="/member/register" className="text-primary hover:underline">
              Đăng ký ngay
            </Link>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
