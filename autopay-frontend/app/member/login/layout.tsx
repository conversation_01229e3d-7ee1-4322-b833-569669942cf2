import { generateDynamicMetadata } from '@/lib/utils/metadata'
import type { Metadata } from 'next'
import { type ReactNode } from 'react'

export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata('<PERSON><PERSON>ng nhập thành viên')
}

export default function Component({ children }: { children: ReactNode }) {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md space-y-8">{children}</div>
    </div>
  )
}
