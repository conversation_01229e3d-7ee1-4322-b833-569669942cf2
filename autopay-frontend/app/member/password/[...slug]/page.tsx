'use client'

import FormItemPasswordGenerator from '@/components/custom-ui/form-item-password-generator'
import { Icons } from '@/components/icons'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { IoIosSend } from 'react-icons/io'
import { toast } from 'sonner'
import { z } from 'zod'

interface ComponentProps {
  params: { slug: string[] }
}

export default function MemberPasswordResetPage({ params }: ComponentProps) {
  const resolvedParams = params

  const router = useRouter()
  const searchParams = useSearchParams()
  const verificationUrl = resolvedParams.slug.join('/') + '?' + searchParams.toString()

  const [reset, setVerified] = useState(false)
  const [data, setData] = useState<ApiResponse>()

  const FormSchema = z
    .object({
      password: z.string().min(8, {
        message: 'Mật khẩu phải có ít nhất 8 ký tự',
      }),
      confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: 'Mật khẩu xác nhận không khớp',
      path: ['confirmPassword'],
    })

  type FormValues = z.infer<typeof FormSchema>

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  // Verify reset link on component mount
  useEffect(() => {
    const verifyLink = async () => {
      try {
        const response = await queryFetchHelper(`/member/password/${verificationUrl}`, {
          method: 'GET',
        })
        setData(response)
        if (response.success) {
          setVerified(true)
        }
      } catch (error: any) {
        setData({
          success: false,
          message: error?.message || 'Liên kết không hợp lệ hoặc đã hết hạn',
        })
      }
    }

    verifyLink()
  }, [verificationUrl])

  const { isPending, mutate } = useMutation({
    mutationFn: (formData: FormValues) => {
      return queryFetchHelper(`/member/password/${verificationUrl}`, {
        method: 'POST',
        body: JSON.stringify({
          password: formData.password,
        }),
      })
    },
    onSuccess: (response) => {
      if (response.success) {
        toast.success(response.message)
        router.push('/member/login')
      } else {
        toast.error(response.message)
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.message || 'Đã xảy ra lỗi'
      toast.error(errorMessage)
    },
  })

  const onSubmit = (formData: FormValues) => {
    mutate(formData)
  }

  if (!data) {
    return (
      <Card className="mx-auto w-full max-w-md">
        <CardContent className="flex items-center justify-center p-6">
          <Icons.spinner className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  if (!reset) {
    return (
      <Card className="mx-auto w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-center text-2xl">Liên kết không hợp lệ</CardTitle>
          <CardDescription className="text-center">
            Liên kết đặt lại mật khẩu không hợp lệ hoặc đã hết hạn
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertDescription>{data.message}</AlertDescription>
          </Alert>
          <div className="text-center">
            <Link
              href="/member/forgot"
              className="text-primary hover:underline">
              Yêu cầu liên kết mới
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="mx-auto w-full max-w-md">
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl">Đặt lại mật khẩu</CardTitle>
        <CardDescription className="text-center">Nhập mật khẩu mới cho tài khoản của bạn</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4">
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mật khẩu mới</FormLabel>
                  <FormControl>
                    <FormItemPasswordGenerator
                      placeholder="Nhập mật khẩu mới"
                      disabled={isPending}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Xác nhận mật khẩu</FormLabel>
                  <FormControl>
                    <FormItemPasswordGenerator
                      placeholder="Nhập lại mật khẩu mới"
                      disabled={isPending}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full"
              disabled={isPending}>
              {isPending ? (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <IoIosSend className="mr-2 h-4 w-4" />
              )}
              Đặt lại mật khẩu
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
