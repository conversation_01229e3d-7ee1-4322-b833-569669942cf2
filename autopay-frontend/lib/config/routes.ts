import { SingleCategoryRoute } from '@/lib/types/menu-route'
import { Home } from 'lucide-react'
import { CgProfile } from 'react-icons/cg'
import { RiBankFill, RiBillFill, RiListSettingsFill, RiProfileLine, RiShareForwardBoxLine } from 'react-icons/ri'

import { BiSolidBellRing, BiSolidStore } from 'react-icons/bi'
import { BsDisplay, BsFillFileEarmarkSpreadsheetFill, BsMicrosoftTeams, BsSendFill } from 'react-icons/bs'
import { FaMoneyBillTransfer, FaSlideshare } from 'react-icons/fa6'
import { GoHistory } from 'react-icons/go'
import { IoIosStats } from 'react-icons/io'
import { IoSettings } from 'react-icons/io5'
import { LuWorkflow } from 'react-icons/lu'
import { MdAccountBox, MdBusinessCenter, MdOutlineWebhook } from 'react-icons/md'
import { PiPackageBold } from 'react-icons/pi'
import { TbApi } from 'react-icons/tb'

import { CiShop } from 'react-icons/ci'
import { FaDiscord, FaShopify, FaSkype, FaSlack, FaTelegram, FaWhmcs } from 'react-icons/fa'
import { FaBuilding, FaBuildingUser } from 'react-icons/fa6'
import { LuPackagePlus } from 'react-icons/lu'
import { SiAirtable, SiGooglechat, SiMattermost, SiWoo, SiZalo } from 'react-icons/si'

const integrationRoutes = [
  {
    label: 'Nền tảng nhắn tin',
    routes: [
      {
        name: 'Telegram',
        icon: FaTelegram,
        path: 'integrations/telegram',
        description: 'Telegram là ứng dụng nhắn tin được sử dụng rộng rãi trên toàn thế giới. ',
      },
      {
        name: 'Slack',
        icon: FaSlack,
        path: 'integrations/slack',
        description: 'Slack là ứng dụng nhắn tin được sử dụng rộng rãi trong các tổ chức và doanh nghiệp.',
      },
      {
        name: 'Zalo OA',
        icon: SiZalo,
        path: 'integrations/zalo-oa',
        description: 'Zalo OA là ứng dụng nhắn tin được sử dụng rộng rãi tại Việt Nam.',
      },
      {
        name: 'Lark Messenger',
        icon: PiPackageBold,
        path: 'integrations/lark-messenger',
        description: 'Lark Messenger là ứng dụng nhắn tin được sử dụng rộng rãi trên cộng đồng Larksuite.',
      },
      {
        name: 'Discord',
        icon: FaDiscord,
        path: 'integrations/discord',
        description: 'Discord là ứng dụng nhắn tin được sử dụng rộng rãi trong cộng đồng game thủ.',
      },
      {
        name: 'Go High Level',
        icon: PiPackageBold,
        path: 'integrations/go-high-level',
        description: 'GoHighLevel là ứng dụng nhắn tin được sử dụng rộng rãi.',
      },
      {
        name: 'Google Chat',
        icon: SiGooglechat,
        path: 'integrations/google-chat',
        description: 'Google Chat là ứng dụng nhắn tin được sử dụng rộng rãi đối với doanh nghiệp.',
      },
      {
        name: 'Microsoft Teams',
        icon: BsMicrosoftTeams,
        path: 'integrations/microsoft-teams',
        description: 'Microsoft Teams là ứng dụng nhắn tin được sử dụng rộng rãi trong các doanh nghiệp.',
      },
      {
        name: 'Skype',
        icon: FaSkype,
        path: 'integrations/skype',
        description: 'Skype là ứng dụng nhắn tin được sử dụng rộng rãi trên toàn thế giới.',
      },
      {
        name: 'Mattermost',
        icon: SiMattermost,
        path: 'integrations/mattermost',
        description: 'Mattermost là ứng dụng nhắn tin được sử dụng rộng rãi.',
      },
      // {
      //   name: 'Lotus Chat',
      //   icon: PiPackageBold,
      //   path: 'instances',
      //   useGeneralRoute: true,
      // },
    ],
  },

  {
    label: 'Tiện ích & Bảng tính',
    routes: [
      {
        name: 'Webhook',
        icon: MdOutlineWebhook,
        path: 'integrations/webhook',
        description: 'Webhooks là cách đơn giản nhất để gửi thông báo đến ứng dụng của bạn.',
      },
      {
        name: 'Google Sheet',
        icon: BsFillFileEarmarkSpreadsheetFill,
        path: 'integrations/google-sheet',
        description: 'GoogleSheet là ứng dụng bảng tính trực tuyến của Google.',
      },
      {
        name: 'Lark Base',
        icon: PiPackageBold,
        path: 'integrations/lark-base',
        description: 'Lark Base là công cụ quản lý bảng tính mạnh mẽ trên Larksuite.',
      },
      {
        name: 'Power BI',
        icon: PiPackageBold,
        path: 'integrations/power-bi',
        description: 'Power BI là công cụ thống kê và báo cáo của Microsoft.',
      },
      {
        name: 'Looker Studio',
        icon: PiPackageBold,
        path: 'integrations/looker-studio',
        description: 'Looker Studio là công cụ thống kê và báo cáo của Google.',
      },
      {
        name: 'Airtable',
        icon: SiAirtable,
        path: 'integrations/airtable',
        description: 'Airtable là công cụ quản lý bảng tính trực tuyến.',
      },
      {
        name: 'Email',
        icon: BsSendFill,
        path: 'integrations/email',
        description: 'Email tổng hợp thông tin giao dịch được định kỳ gửi đến bạn.',
      },
    ],
  },
  {
    label: 'Thương mại điện tử ',
    routes: [
      {
        name: 'WooCommerce',
        icon: SiWoo,
        path: 'integrations/woocommerce',
        description: 'WooCommerce là nền tảng thương mại điện tử nguồn mở trong cộng đồng sử dụng WordPess.',
      },
      {
        name: 'Shopify',
        icon: FaShopify,
        path: 'integrations/shopify',
        description: 'Shopify là nền tảng thương mại điện tử phổ biến được sử dụng rộng rãi trên toàn thế giới.',
      },
      {
        name: 'Haravan',
        icon: CiShop,
        path: 'integrations/haravan',
        description: 'Haravan là nền tảng thương mại điện tử phổ biến tại Việt Nam.',
      },
      {
        name: 'Sapo',
        icon: CiShop,
        path: 'integrations/sapo',
        description: 'Sapo là nền tảng thương mại điện tử phổ biến tại Việt Nam.',
      },
      {
        name: 'WHMCS',
        icon: FaWhmcs,
        path: 'integrations/whmcs',
        description: 'WHMCS là nền tảng quản lý khách hàng và hóa đơn phổ biến trong ngành hosting.',
      },
      {
        name: 'HostBill',
        icon: PiPackageBold,
        path: 'integrations/hostbill',
        description: 'HostBill là nền tảng quản lý khách hàng và hóa đơn phổ biến trong ngành hosting.',
      },
    ],
  },
]

const routes: SingleCategoryRoute[] = [
  {
    label: 'Dashboard',
    routes: [
      {
        name: 'Tổng quan',
        icon: Home,
        path: '',
        useGeneralRoute: true,
      },
      {
        name: 'Giao dịch',
        icon: FaMoneyBillTransfer,
        path: 'transactions',
        useGeneralRoute: true,
      },
      {
        name: 'Ngân hàng',
        icon: RiBankFill,
        path: 'banks',
        subPaths: ['banks/connect'],
        matchRouteRegex: /^banks/,
      },
      {
        name: 'Thống kê',
        icon: IoIosStats,
        path: 'reports',
        routes: [
          {
            name: 'Profile',
            icon: CgProfile,
            path: '',
          },
          {
            name: 'Account',
            icon: MdAccountBox,
            path: 'account',
          },
          {
            name: 'Appearance',
            icon: BsDisplay,
            path: 'appearance',
          },
          {
            name: 'Notifications',
            icon: BiSolidBellRing,
            path: 'notifications',
          },
          {
            name: 'Display',
            icon: IoSettings,
            path: 'display',
          },
        ],
      },
    ],
  },
  {
    label: 'Tích hợp',
    routes: [
      {
        name: 'Luồng kết nối',
        icon: LuWorkflow,
        path: 'integrations',
        subPaths: ['integrations/connect'],
        matchRouteRegex: /^integrations\/\w{26}$/,
      },
      {
        name: 'Kết nối 3rd-Party',
        icon: BiSolidStore,
        path: 'integrations/connector',
        subPaths: integrationRoutes.flatMap((routeGroup) => routeGroup.routes.map((route) => route.path)),
      },
    ],
  },
  {
    label: 'Cấu hình',
    routes: [
      {
        name: 'Tổ chức',
        icon: FaBuilding,
        path: 'settings',
        subPaths: [
          'settings/account',
          'settings/appearance',
          'settings/notifications',
          'settings/display',
          'settings/theme',
          'settings/domains',
        ],
      },
      {
        name: 'Tài khoản',
        icon: RiProfileLine,
        path: 'profile',
        useGeneralRoute: true,
      },
      {
        name: 'Thành viên',
        icon: FaSlideshare,
        path: 'members',
        useGeneralRoute: true,
      },
      {
        name: 'Hồ sơ công ty',
        icon: MdBusinessCenter,
        path: 'company',
        useGeneralRoute: true,
      },
      {
        name: 'Gói dịch vụ',
        icon: LuPackagePlus,
        path: 'packages',
        useGeneralRoute: true,
      },
      {
        name: 'Hóa đơn',
        icon: RiBillFill,
        path: 'billing',
        useGeneralRoute: true,
      },
      {
        name: 'Tiếp thị liên kết',
        icon: RiShareForwardBoxLine,
        path: 'affiliate',
        useGeneralRoute: true,
      },
    ],
  },
  {
    label: 'Developers',
    routes: [
      {
        name: 'API',
        icon: TbApi,
        path: 'api',
        useGeneralRoute: true,
      },
      {
        name: 'Nhật ký',
        icon: GoHistory,
        path: 'logs',
        useGeneralRoute: true,
      },
    ],
  },
]

export { integrationRoutes, routes }
