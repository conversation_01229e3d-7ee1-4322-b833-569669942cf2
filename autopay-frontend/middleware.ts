import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import globalConfig from './lib/config'
import { fetchDomainConfigForMiddleware } from './lib/server/domain'

export async function middleware(request: NextRequest): Promise<NextResponse> {
  const { cookies, nextUrl } = request
  const pathname = nextUrl.pathname
  const isLoggedIn = cookies.has(process.env.NEXT_PUBLIC_APP_NAME + '.authorization')

  // Use global configuration
  const publicPaths = globalConfig.publicPaths
  const authPaths = globalConfig.authPaths
  const memberAuthPaths = globalConfig.memberAuthPaths

  /**
   * Check if a path matches exactly or is a sub-path
   */
  const isPathMatch = (paths: string[], currentPath: string): boolean => {
    return paths.some((path) => currentPath === path || currentPath.startsWith(`${path}/`))
  }

  const isPublicPage = isPathMatch(publicPaths, pathname)
  const isAuthPage = isPathMatch(authPaths, pathname)
  const isMemberAuthPage = isPathMatch(memberAuthPaths, pathname)

  // Create response
  let response = NextResponse.next()

  // Handle domain config and potential errors
  const domainResult = await handleDomainConfig(request, response)

  // If the domain is not supported, return 404
  if (domainResult?.error) {
    return new NextResponse('Domain not supported', { status: 404 })
  }

  // Redirect logic
  if (isLoggedIn) {
    // If a user is logged in and tries to access auth pages, redirect to home
    if (isAuthPage || isMemberAuthPage) {
      response = NextResponse.redirect(new URL('/', request.url))
    }
  } else if (!isPublicPage && !isAuthPage && !isMemberAuthPage) {
    // If a user is not logged in and tries to access non-public, non-auth pages, redirect to log in
    response = NextResponse.redirect(new URL('/login', request.url))
  }

  return response
}

/**
 * Handle domain config using centralized logic from domain.ts
 * Returns error if the domain is not supported
 */
async function handleDomainConfig(request: NextRequest, response: NextResponse) {
  const hostname = request.headers.get('host') as string
  const result = await fetchDomainConfigForMiddleware(hostname)

  // Set the header if config is available
  if (result.shouldSetHeader && result.config) {
    try {
      // Encode to base64 to handle Unicode characters in headers
      const configJson = JSON.stringify(result.config)
      const encodedConfig = Buffer.from(configJson, 'utf-8').toString('base64')
      response.headers.set('x-domain-config', encodedConfig)
    } catch (error) {
      console.warn('Failed to encode domain config for header:', error)
    }
  }

  return result
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',
  ],
}
