<?php

namespace Modules\Customer\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Modules\Customer\Models\Customer;
use Modules\User\Models\Role;

class TeammateController extends Controller
{
    /**
     * Get list of teammates for current customer
     */
    public function index(Request $request): JsonResponse
    {
        $customer = Auth::guard('customer')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Không có quyền truy cập.',
            ], 403);
        }

        $teammates = $customer->teammates()
            ->where('customer_teammates.is_active', true)
            ->get()
            ->map(function ($teammate) {
                // Get teammate's roles and permissions
                $roles = $teammate->getRoleNames('customer')->toArray();
                $permissions = $teammate->getPermissionsViaRoles()
                    ->where('guard_name', 'customer')
                    ->pluck('name')
                    ->toArray();

                return [
                    'id' => $teammate->id,
                    'first_name' => $teammate->first_name,
                    'last_name' => $teammate->last_name,
                    'full_name' => $teammate->full_name,
                    'email' => $teammate->email,
                    'phone' => $teammate->phone,
                    'roles' => $roles,
                    'permissions' => $permissions,
                    'added_at' => $teammate->pivot->created_at,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'teammates' => $teammates,
                'total' => $teammates->count(),
            ],
        ]);
    }

    /**
     * Add a new teammate
     */
    public function store(Request $request): JsonResponse
    {
        $customer = Auth::guard('customer')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Không có quyền truy cập.',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
            'role' => 'required|string|exists:roles,name',
            'permissions' => 'sometimes|array',
            'permissions.*' => 'string|exists:permissions,name',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ.',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Find teammate customer by email and password
        $teammate = Customer::where('email', $request->email)
            ->where('organization_id', $customer->organization_id)
            ->where('is_active', true)
            ->first();

        if (!$teammate || !Hash::check($request->password, $teammate->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy customer với email và password này.',
            ], 404);
        }

        // Check if customer is trying to add themselves
        if ($teammate->id === $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể thêm chính mình làm teammate.',
            ], 422);
        }

        // Check if teammate already exists
        if ($customer->hasTeammate($teammate)) {
            return response()->json([
                'success' => false,
                'message' => 'Customer này đã là teammate của bạn.',
            ], 422);
        }

        // Verify role exists and is for customer guard
        $role = Role::where('name', $request->role)
            ->where('guard_name', 'customer')
            ->first();

        if (!$role) {
            return response()->json([
                'success' => false,
                'message' => 'Role không hợp lệ.',
            ], 422);
        }

        // Add teammate
        $success = $customer->addTeammate($teammate, $request->role);

        if (!$success) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể thêm teammate.',
            ], 500);
        }

        // Add additional permissions if provided
        if ($request->has('permissions')) {
            $teammate->givePermissionTo($request->permissions, 'customer');
        }

        return response()->json([
            'success' => true,
            'message' => 'Thêm teammate thành công.',
            'data' => [
                'teammate' => [
                    'id' => $teammate->id,
                    'first_name' => $teammate->first_name,
                    'last_name' => $teammate->last_name,
                    'full_name' => $teammate->full_name,
                    'email' => $teammate->email,
                    'role' => $request->role,
                    'permissions' => $request->permissions ?? [],
                ],
            ],
        ]);
    }

    /**
     * Update teammate permissions
     */
    public function updatePermissions(Request $request, string $teammateId): JsonResponse
    {
        $customer = Auth::guard('customer')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Không có quyền truy cập.',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'role' => 'sometimes|string|exists:roles,name',
            'permissions' => 'sometimes|array',
            'permissions.*' => 'string|exists:permissions,name',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ.',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Find teammate
        $teammate = $customer->teammates()
            ->where('teammate_customer_id', $teammateId)
            ->where('customer_teammates.is_active', true)
            ->first();

        if (!$teammate) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy teammate.',
            ], 404);
        }

        // Update role if provided
        if ($request->has('role')) {
            $role = Role::where('name', $request->role)
                ->where('guard_name', 'customer')
                ->first();

            if (!$role) {
                return response()->json([
                    'success' => false,
                    'message' => 'Role không hợp lệ.',
                ], 422);
            }

            // Remove old roles and assign new role
            $teammate->syncRoles([$request->role], 'customer');
        }

        // Update permissions if provided
        if ($request->has('permissions')) {
            $teammate->syncPermissions($request->permissions, 'customer');
        }

        return response()->json([
            'success' => true,
            'message' => 'Cập nhật quyền teammate thành công.',
            'data' => [
                'teammate' => [
                    'id' => $teammate->id,
                    'roles' => $teammate->getRoleNames('customer')->toArray(),
                    'permissions' => $teammate->getPermissionsViaRoles()
                        ->where('guard_name', 'customer')
                        ->pluck('name')
                        ->toArray(),
                ],
            ],
        ]);
    }

    /**
     * Remove a teammate
     */
    public function destroy(string $teammateId): JsonResponse
    {
        $customer = Auth::guard('customer')->user();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Không có quyền truy cập.',
            ], 403);
        }

        // Find teammate
        $teammate = $customer->teammates()
            ->where('teammate_customer_id', $teammateId)
            ->where('customer_teammates.is_active', true)
            ->first();

        if (!$teammate) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy teammate.',
            ], 404);
        }

        // Remove teammate
        $success = $customer->removeTeammate($teammate);

        if (!$success) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể xóa teammate.',
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Xóa teammate thành công.',
        ]);
    }

    /**
     * Get available roles and permissions for teammates
     */
    public function getRolesAndPermissions(): JsonResponse
    {
        $roles = Role::where('guard_name', 'customer')->get(['name', 'display_name', 'description']);
        $permissions = \Modules\User\Models\Permission::where('guard_name', 'customer')
            ->get(['name', 'description']);

        return response()->json([
            'success' => true,
            'data' => [
                'roles' => $roles,
                'permissions' => $permissions,
            ],
        ]);
    }
}
