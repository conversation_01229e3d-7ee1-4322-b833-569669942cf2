<?php

namespace Modules\Member\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Hash;
use Modules\Member\Models\Member;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', 'max:255'],
            'password' => ['required', 'string', 'min:6'],
            'remember' => ['sometimes', 'boolean'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'email.required' => 'Vui lòng nhập địa chỉ email.',
            'email.email' => 'Địa chỉ email không hợp lệ.',
            'email.max' => 'Địa chỉ email không được vượt quá 255 ký tự.',
            'password.required' => '<PERSON>ui lòng nhập mật khẩu.',
            'password.string' => 'Mật khẩu phải là chuỗi ký tự.',
            'password.min' => '<PERSON><PERSON>t khẩu phải có ít nhất 6 ký tự.',
            'remember.boolean' => 'Giá trị ghi nhớ không hợp lệ.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if ($validator->errors()->any()) {
                return;
            }

            // Get current organization from domain context
            $organization = app('current.organization');
            
            if (!$organization) {
                $validator->errors()->add('email', 'Không thể xác định tổ chức từ domain này.');
                return;
            }

            // Check if member exists and credentials are valid
            $member = Member::where('email', $this->email)
                          ->where('organization_id', $organization->id)
                          ->where('is_active', true)
                          ->first();

            if (!$member || !Hash::check($this->password, $member->password)) {
                $validator->errors()->add('email', 'Thông tin đăng nhập không chính xác.');
                return;
            }

            // Check if member's organization is active
            if (!$organization->is_active) {
                $validator->errors()->add('email', 'Tổ chức này hiện không hoạt động.');
                return;
            }
        });
    }
}
