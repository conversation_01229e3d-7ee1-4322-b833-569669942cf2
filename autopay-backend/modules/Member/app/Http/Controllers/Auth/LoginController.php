<?php

namespace Modules\Member\Http\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Modules\Member\Http\Requests\Auth\LoginRequest;
use Modules\Member\Models\Member;

class LoginController extends Controller
{
    /**
     * Handle member login request.
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $organization = app('current.organization');

        if (!$organization) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể xác định tổ chức từ domain này.',
            ], 400);
        }

        // Find member in organization
        $member = Member::where('email', $request->email)
                      ->where('organization_id', $organization->id)
                      ->where('is_active', true)
                      ->first();

        if (!$member || !Hash::check($request->password, $member->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Thông tin đăng nhập không chính xác.',
            ], 401);
        }

        // Create token for member
        $token = $member->createToken('member-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Đăng nhập thành công.',
            'data' => [
                'member' => [
                    'id' => $member->id,
                    'first_name' => $member->first_name,
                    'last_name' => $member->last_name,
                    'full_name' => $member->full_name,
                    'email' => $member->email,
                    'phone' => $member->phone,
                    'organization' => [
                        'id' => $organization->id,
                        'name' => $organization->name,
                    ],
                ],
                'token' => $token,
            ],
        ]);
    }

    /**
     * Handle member logout request.
     */
    public function logout(Request $request): JsonResponse
    {
        $member = Auth::guard('member')->user();

        if ($member) {
            // Revoke current token
            $member->currentAccessToken()->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'Đăng xuất thành công.',
        ]);
    }

    /**
     * Get current authenticated member.
     */
    public function me(Request $request): JsonResponse
    {
        $member = Auth::guard('member')->user();
        $organization = app('current.organization');

        if (!$member) {
            return response()->json([
                'success' => false,
                'message' => 'Chưa đăng nhập.',
            ], 401);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'member' => [
                    'id' => $member->id,
                    'first_name' => $member->first_name,
                    'last_name' => $member->last_name,
                    'full_name' => $member->full_name,
                    'email' => $member->email,
                    'phone' => $member->phone,
                    'organization' => [
                        'id' => $organization->id,
                        'name' => $organization->name,
                    ],
                ],
            ],
        ]);
    }
}
