<?php

namespace Modules\Member\Http\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Hash;
use Modules\Member\Http\Requests\Auth\RegisterRequest;
use Modules\Member\Models\Member;

class RegisterController extends Controller
{
    /**
     * Handle member registration request.
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        $organization = app('current.organization');

        if (!$organization) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể xác định tổ chức từ domain này.',
            ], 400);
        }

        if (!$organization->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Tổ chức này hiện không hoạt động.',
            ], 400);
        }

        // Create new member
        $member = Member::create([
            'organization_id' => $organization->id,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'is_active' => true,
        ]);

        // Create token for member
        $token = $member->createToken('member-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Đăng ký thành công.',
            'data' => [
                'member' => [
                    'id' => $member->id,
                    'first_name' => $member->first_name,
                    'last_name' => $member->last_name,
                    'full_name' => $member->full_name,
                    'email' => $member->email,
                    'phone' => $member->phone,
                    'organization' => [
                        'id' => $organization->id,
                        'name' => $organization->name,
                    ],
                ],
                'token' => $token,
            ],
        ], 201);
    }
}
