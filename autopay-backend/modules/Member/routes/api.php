<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Member\Http\Controllers\Auth\ForgotPasswordController;
use Modules\Member\Http\Controllers\Auth\LoginController;
use Modules\Member\Http\Controllers\Auth\RegisterController;
use Modules\Member\Http\Controllers\MemberController;
use Modules\Organization\Http\Middleware\ResolveDomainOrganization;

Route::group([
    'middleware' => ResolveDomainOrganization::class,
], static function () {

    // Test route
    Route::get('/member/test', function () {
        $organization = null;
        try {
            $organization = app('current.organization');
        } catch (\Exception $e) {
            // Organization isn't bound
        }

        return response()->json([
            'message' => 'Member routes working',
            'organization' => $organization?->name ?? 'No organization',
            'timestamp' => now(),
        ]);
    });

    // Simple register test
    Route::post('/member/register-test', static function (\Illuminate\Http\Request $request) {
        $organization = app('current.organization');

        return response()->json([
            'message' => 'Register test',
            'organization' => $organization?->name ?? 'No organization',
            'data' => $request->all(),
        ]);
    });

    // Register without FormRequest validation
    Route::post('/member/register-simple', static function (\Illuminate\Http\Request $request) {
        $organization = app('current.organization');

        if (! $organization) {
            return response()->json(['error' => 'No organization found'], 400);
        }

        // Simple validation
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'password' => 'required|string|min:6',
        ]);

        // Create member
        $member = \Modules\Member\Models\Member::create([
            'organization_id' => $organization->id,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => \Illuminate\Support\Facades\Hash::make($request->password),
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Member created successfully',
            'member' => $member,
        ]);
    });

    // Register with FormRequest but no guest middleware
    Route::post('/member/register-no-middleware', [\Modules\Member\Http\Controllers\Auth\RegisterController::class, 'register']);

    // Test auth
    Route::get('/member/auth-test', function () {
        return response()->json([
            'message' => 'Auth test',
            'user' => auth('member')->user(),
            'guard' => auth()->getDefaultDriver(),
            'authenticated' => auth('member')->check(),
        ]);
    })->middleware('auth:member');

    // Test token without auth middleware
    Route::get('/member/token-test', function (\Illuminate\Http\Request $request) {
        $token = $request->bearerToken();
        $user = null;

        if ($token) {
            // Try to find token in database
            $accessToken = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if ($accessToken) {
                $user = $accessToken->tokenable;
            }
        }

        return response()->json([
            'message' => 'Token test',
            'has_token' => ! empty($token),
            'token_preview' => $token ? substr($token, 0, 20).'...' : null,
            'user' => $user,
            'auth_member' => auth('member')->user(),
            'auth_check' => auth('member')->check(),
        ]);
    });

    // Member authentication routes (guest only)
    Route::group([
        'prefix' => 'member',
    ], static function () {
        // Guest routes (no authentication required)
        Route::group([
            'middleware' => 'guest:member',
        ], static function () {
            Route::post('/login', [LoginController::class, 'login'])->name('member.login');
            Route::post('/register', [RegisterController::class, 'register'])->name('member.register');
            Route::post('/forgot', [ForgotPasswordController::class, 'forgot'])->name('member.forgot');
        });

        // Password reset routes (no guest middleware needed for reset link)
        Route::group([
            'controller' => ForgotPasswordController::class,
            'middleware' => 'throttle:5,1',
        ], static function () {
            Route::get('/password/reset/{id}/{hash}', 'passwordReset')->name('member.password.reset')
                ->middleware(['signed:relative']);
        });

        // Authenticated member routes
        Route::group([
            'middleware' => 'auth:member',
        ], static function () {
            Route::post('/logout', [LoginController::class, 'logout'])->name('member.logout');
            Route::get('/me', [LoginController::class, 'me'])->name('member.me');

            // Member dashboard and profile
            Route::get('/dashboard', [MemberController::class, 'dashboard'])->name('member.dashboard');
            Route::put('/profile', [MemberController::class, 'updateProfile'])->name('member.profile.update');
        });
    });
});
